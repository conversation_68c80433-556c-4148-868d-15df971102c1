import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Form, Input, Select, InputNumber, Checkbox, Row, Col } from 'antd';

import { BUTTON } from '@constant';
import AntButton from '@component/AntButton';
import { toast } from '@component/ToastProvider';

import {
  createAIScenario,
  updateAIScenario
} from '@services/RolePlay/AIScenarioService';
import { getAllAIPersonasWithoutPagination } from '@services/RolePlay/AIPersonaService';
import { getAllInstruction } from '@services/RolePlay/RolePlayInstructionService';
import { getAllRoleplayTasksWithoutPagination } from '@services/RolePlay/TaskService';

const { TextArea } = Input;
const { Option } = Select;

const AIScenarioForm = ({ visible, courseId, scenario, onSuccess, onCancel }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [aiPersonas, setAiPersonas] = useState([]);
  const [roleplayInstructions, setRoleplayInstructions] = useState([]);
  const [tasks, setTasks] = useState([]);

  const isEditMode = !!scenario;

  useEffect(() => {
    if (visible) {
      fetchFormData();
      if (isEditMode) {
        form.setFieldsValue({
          name: scenario.name,
          description: scenario.description,
          aiPersonaId: scenario.aiPersonaId?._id,
          roleplayInstructionId: scenario.roleplayInstructionId?._id,
          taskIds: scenario.taskIds?.map(task => task._id) || [],
          estimatedCallTimeInMinutes: scenario.estimatedCallTimeInMinutes,
          isDefault: scenario.isDefault,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, scenario, isEditMode]);

  const fetchFormData = async () => {
    try {
      const [personasResponse, instructionsResponse, tasksResponse] = await Promise.all([
        getAllAIPersonasWithoutPagination({}, [], false),
        getAllInstruction({}),
        getAllRoleplayTasksWithoutPagination({}, [], false)
      ]);

      if (personasResponse) {
        setAiPersonas(personasResponse);
      }
      if (instructionsResponse) {
        setRoleplayInstructions(instructionsResponse);
      }
      if (tasksResponse) {
        setTasks(tasksResponse);
      }
    } catch (error) {
      console.error('Error fetching form data:', error);
      toast.error(t('ERROR_FETCHING_FORM_DATA'));
    }
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const scenarioData = {
        ...values,
        courseId,
      };

      if (isEditMode) {
        scenarioData._id = scenario._id;
        await updateAIScenario(scenarioData, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
        toast.success(t('UPDATE_SCENARIO_SUCCESS'));
      } else {
        await createAIScenario(scenarioData, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
        toast.success(t('CREATE_SCENARIO_SUCCESS'));
      }

      onSuccess();
    } catch (error) {
      console.error('Error saving scenario:', error);
      if (isEditMode) {
        toast.error(t('UPDATE_SCENARIO_ERROR'));
      } else {
        toast.error(t('CREATE_SCENARIO_ERROR'));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={isEditMode ? t('EDIT_AI_SCENARIO') : t('CREATE_AI_SCENARIO')}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <AntButton key="cancel" onClick={handleCancel}>
          {t('CANCEL')}
        </AntButton>,
        <AntButton
          key="submit"
          type={BUTTON.DEEP_NAVY}
          loading={loading}
          onClick={() => form.submit()}
        >
          {isEditMode ? t('UPDATE') : t('CREATE')}
        </AntButton>,
      ]}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          isDefault: false,
        }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="name"
              label={t('SCENARIO_NAME')}
              rules={[
                { required: true, message: t('PLEASE_ENTER_SCENARIO_NAME') },
                { max: 100, message: t('SCENARIO_NAME_TOO_LONG') }
              ]}
            >
              <Input placeholder={t('ENTER_SCENARIO_NAME')} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="description"
              label={t('SCENARIO_DESCRIPTION')}
              rules={[
                { max: 500, message: t('SCENARIO_DESCRIPTION_TOO_LONG') }
              ]}
            >
              <TextArea
                rows={3}
                placeholder={t('ENTER_SCENARIO_DESCRIPTION')}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="aiPersonaId"
              label={t('AI_PERSONA')}
              rules={[
                { required: true, message: t('PLEASE_SELECT_AI_PERSONA') }
              ]}
            >
              <Select
                placeholder={t('SELECT_AI_PERSONA')}
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {aiPersonas.map(persona => (
                  <Option key={persona._id} value={persona._id}>
                    {persona.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="roleplayInstructionId"
              label={t('ROLEPLAY_INSTRUCTION')}
            >
              <Select
                placeholder={t('SELECT_ROLEPLAY_INSTRUCTION')}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {roleplayInstructions.map(instruction => (
                  <Option key={instruction._id} value={instruction._id}>
                    {instruction.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={16}>
            <Form.Item
              name="taskIds"
              label={t('TASKS')}
            >
              <Select
                mode="multiple"
                placeholder={t('SELECT_TASKS')}
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {tasks.map(task => (
                  <Option key={task._id} value={task._id}>
                    {task.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="estimatedCallTimeInMinutes"
              label={t('ESTIMATED_TIME_MINUTES')}
              rules={[
                { required: true, message: t('PLEASE_ENTER_ESTIMATED_CALL_TIME') }
              ]}
            >
              <InputNumber
                min={1}
                max={120}
                placeholder={t('ENTER_ESTIMATED_TIME')}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="isDefault"
              valuePropName="checked"
            >
              <Checkbox>
                {t('SET_AS_DEFAULT_SCENARIO')}
              </Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default AIScenarioForm;
